<?php

namespace app\modules\employer\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;

/**
 * Модель работодателя
 * 
 * @property int $id
 * @property string $name
 * @property string $phone
 * @property string $business_name
 * @property string $business_inn
 * @property string $business_address
 * @property int $status
 * @property string $created_at
 * @property string $deleted_at
 */
class Employer extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%employers}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'phone'], 'required'],
            [['status'], 'integer'],
            [['created_at', 'deleted_at'], 'safe'],
            [['name'], 'string', 'max' => 100],
            [['phone'], 'string', 'max' => 20],
            [['business_name', 'business_address'], 'string', 'max' => 255],
            [['business_inn'], 'string', 'max' => 20],
            [['phone'], 'unique'],
            [['business_inn'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Имя работодателя',
            'phone' => 'Телефон',
            'business_name' => 'Название компании',
            'business_inn' => 'ИНН',
            'business_address' => 'Адрес компании',
            'status' => 'Статус',
            'created_at' => 'Дата создания',
            'deleted_at' => 'Дата удаления',
        ];
    }

    /**
     * Получение вакансий работодателя
     */
    public function getVacancies()
    {
        return $this->hasMany(Vacancy::class, ['employer_id' => 'id'])
            ->where(['deleted_at' => null]);
    }

    /**
     * Получение избранных работников
     */
    public function getFavoriteWorkers()
    {
        return $this->hasMany(\app\modules\worker\models\Worker::class, ['id' => 'worker_id'])
            ->viaTable('{{%employer_favorites}}', ['employer_id' => 'id'])
            ->where(['{{%employer_favorites}}.deleted_at' => null]);
    }
} 