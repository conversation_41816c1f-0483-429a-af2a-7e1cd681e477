# SMS Authentication API для Worker модуля

## Обзор

Новая система аутентификации использует SMS-коды для валидации входа в систему и поддерживает автоматическую регистрацию незарегистрированных пользователей. Процесс состоит из двух этапов:

1. **Отправка кода** - пользователь запрашивает код валидации на свой номер телефона
2. **Вход в систему** - пользователь вводит полученный код для получения токена доступа

## Новые возможности

### Автоматическая регистрация
- Если worker с указанным номером телефона не существует, система автоматически создает минимальную запись
- Минимальная запись содержит только номер телефона и статус профиля "incomplete"
- Пользователь может войти в систему, но должен завершить профиль для доступа к полному функционалу

### Состояния профиля
- **incomplete** - профиль не завершен (только номер телефона)
- **complete** - профиль завершен (заполнены имя, возраст, описание, профессии)

## Настройка

### Конфигурация eskiz.uz

В файле `config/params.php` добавлены настройки для SMS сервиса:

```php
'eskiz' => [
    'phone' => '+998901234567', // Ваш номер телефона для eskiz.uz
    'from' => '4546', // Отправитель SMS
    'enabled' => true, // Включить/выключить отправку SMS
],
```

## API Endpoints

### 1. Отправка кода валидации

**Endpoint:** `POST /worker/auth/send-code`

**Описание:** Отправляет SMS с кодом валидации на указанный номер телефона.

**Параметры:**
- `phone` (string, обязательный) - Номер телефона worker'а

**Ограничения:**
- Повторный запрос возможен только через 2 минуты после предыдущего
- Код действителен 5 минут

**Пример запроса:**
```bash
curl -X POST http://localhost:8000/worker/auth/send-code \
  -d "phone=+998901234567"
```

**Успешный ответ (200):**
```json
{
  "success": true,
  "data": {
    "message": "Validation code sent to your phone",
    "phone": "+998901234567",
    "expires_in": 300,
    "next_request_in": 120,
    "is_new_registration": true,
    "profile_status": "incomplete",
    "registration_info": {
      "message": "Welcome! Please complete your profile after login",
      "telegram_bot_url": "https://t.me/your_bot_name",
      "requires_profile_completion": true
    }
  },
  "message": "Code sent successfully"
}
```

**Поля ответа:**
- `is_new_registration` - true, если это новый пользователь
- `profile_status` - статус профиля (incomplete/complete)
- `registration_info` - дополнительная информация для новых пользователей (только если is_new_registration = true)

**Ошибки:**
- `422` - Номер телефона не указан
- `404` - Worker с таким номером не найден
- `429` - Слишком частые запросы (нужно подождать)
- `500` - Ошибка отправки SMS

**Пример ошибки (429):**
```json
{
  "success": false,
  "data": {
    "remaining_seconds": 85
  },
  "message": "Please wait 85 seconds before requesting a new code"
}
```

### 2. Вход в систему с кодом

**Endpoint:** `POST /worker/auth/login`

**Описание:** Авторизует пользователя по номеру телефона и коду валидации.

**Параметры:**
- `phone` (string, обязательный) - Номер телефона worker'а
- `code` (string, обязательный) - Код валидации из SMS

**Пример запроса:**
```bash
curl -X POST http://localhost:8000/worker/auth/login \
  -d "phone=+998901234567" \
  -d "code=1234"
```

**Успешный ответ (200):**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_at": "2024-01-15 10:30:00",
    "worker": {
      "id": 1,
      "phone": "+998901234567",
      "name": "Test Worker",
      "profile_status": "incomplete",
      "is_profile_complete": false,
      "is_minimal_profile": true,
      // ... другие поля профиля
    },
    "profile_completion_required": true,
    "profile_completion_message": "Please complete your profile to access all features"
  },
  "message": "Successfully authenticated"
}
```

**Новые поля в профиле worker:**
- `profile_status` - статус профиля (incomplete/complete)
- `is_profile_complete` - булево значение, завершен ли профиль
- `is_minimal_profile` - булево значение, является ли профиль минимальным

**Дополнительные поля ответа:**
- `profile_completion_required` - true, если требуется завершение профиля
- `profile_completion_message` - сообщение о необходимости завершения профиля

**Ошибки:**
- `422` - Не указан номер телефона или код
- `400` - Неверный или истекший код валидации
- `404` - Worker с таким номером не найден

## Процесс аутентификации

### Шаг 1: Запрос кода
```
Клиент -> POST /worker/auth/send-code
       -> phone: +998901234567

Сервер -> Генерирует 4-значный код
       -> Сохраняет код в кеше на 5 минут
       -> Отправляет SMS через eskiz.uz
       -> Сохраняет время отправки для ограничения повторных запросов
       -> Возвращает успешный ответ
```

### Шаг 2: Вход в систему
```
Клиент -> POST /worker/auth/login
       -> phone: +998901234567
       -> code: 1234

Сервер -> Проверяет код в кеше
       -> Удаляет код после успешной проверки
       -> Находит worker'а в базе данных
       -> Генерирует JWT токен на 7 дней
       -> Логирует вход в систему
       -> Возвращает токен и профиль пользователя
```

## Безопасность

1. **Ограничение частоты запросов:** Код можно запросить не чаще чем раз в 2 минуты
2. **Время жизни кода:** Код действителен только 5 минут
3. **Одноразовое использование:** Код удаляется после успешной проверки
4. **Токен доступа:** JWT токен действителен 7 дней

## Логирование

Все события аутентификации логируются через `LoggingService`:
- Успешные входы в систему
- Неудачные попытки входа
- Обновление токенов

## Интеграция с eskiz.uz

SMS отправляются через API eskiz.uz:
- **URL:** `https://notify.eskiz.uz/api`
- **Авторизация:** По номеру телефона
- **Формат сообщения:** "Sizning Ishchi hisobingizga kirish uchun tasdiqlash kodi: Kod: XXXX"

## Проверка завершенности профиля

### Ограничения доступа

Пользователи с незавершенным профилем имеют ограниченный доступ к функциям:

**Разрешенные действия без завершенного профиля:**
- Авторизация (send-code, login, logout, verify, refresh)
- Просмотр профиля (profile/index)
- Обновление профиля (profile/update)
- Загрузка аудио (profile/upload-audio)

**Требуют завершенного профиля:**
- Просмотр списка вакансий (vacancy/list)
- Поиск вакансий (vacancy/search)
- Детальный просмотр вакансий (vacancy/detail)
- Работа с избранным (profile/add-favorite, profile/remove-favorite, profile/favorites)

### Ответ при незавершенном профиле

Если пользователь пытается получить доступ к функции, требующей завершенного профиля:

```json
{
  "success": false,
  "data": {
    "profile_completion_required": true,
    "message": "Please complete your profile to access this feature",
    "profile_status": "incomplete",
    "completion_steps": {
      "name": true,
      "age": true,
      "about": true,
      "professions": true
    }
  },
  "message": "Profile completion required"
}
```

**HTTP статус:** 403 Forbidden

### Автоматическое завершение профиля

Профиль автоматически помечается как завершенный при заполнении всех обязательных полей:
- Имя (name)
- Возраст (age)
- Описание (about)
- Хотя бы одна профессия

## Тестирование

Для тестирования API можно использовать:
1. `test_sms_api.php` - тестирование SMS сервиса
2. `test_api_endpoints.php` - тестирование HTTP endpoints
3. Postman/curl для ручного тестирования
