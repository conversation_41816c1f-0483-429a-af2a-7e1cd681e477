# SMS Authentication API для Worker модуля

## Обзор

Новая система аутентификации использует SMS-коды для валидации входа в систему. Процесс состоит из двух этапов:

1. **Отправка кода** - пользователь запрашивает код валидации на свой номер телефона
2. **Вход в систему** - пользователь вводит полученный код для получения токена доступа

## Настройка

### Конфигурация eskiz.uz

В файле `config/params.php` добавлены настройки для SMS сервиса:

```php
'eskiz' => [
    'phone' => '+998901234567', // Ваш номер телефона для eskiz.uz
    'from' => '4546', // Отправитель SMS
    'enabled' => true, // Включить/выключить отправку SMS
],
```

## API Endpoints

### 1. Отправка кода валидации

**Endpoint:** `POST /worker/auth/send-code`

**Описание:** Отправляет SMS с кодом валидации на указанный номер телефона.

**Параметры:**
- `phone` (string, обязательный) - Номер телефона worker'а

**Ограничения:**
- Повторный запрос возможен только через 2 минуты после предыдущего
- Код действителен 5 минут

**Пример запроса:**
```bash
curl -X POST http://localhost:8000/worker/auth/send-code \
  -d "phone=+998901234567"
```

**Успешный ответ (200):**
```json
{
  "success": true,
  "data": {
    "message": "Validation code sent to your phone",
    "phone": "+998901234567",
    "expires_in": 300,
    "next_request_in": 120
  },
  "message": "Code sent successfully"
}
```

**Ошибки:**
- `422` - Номер телефона не указан
- `404` - Worker с таким номером не найден
- `429` - Слишком частые запросы (нужно подождать)
- `500` - Ошибка отправки SMS

**Пример ошибки (429):**
```json
{
  "success": false,
  "data": {
    "remaining_seconds": 85
  },
  "message": "Please wait 85 seconds before requesting a new code"
}
```

### 2. Вход в систему с кодом

**Endpoint:** `POST /worker/auth/login`

**Описание:** Авторизует пользователя по номеру телефона и коду валидации.

**Параметры:**
- `phone` (string, обязательный) - Номер телефона worker'а
- `code` (string, обязательный) - Код валидации из SMS

**Пример запроса:**
```bash
curl -X POST http://localhost:8000/worker/auth/login \
  -d "phone=+998901234567" \
  -d "code=1234"
```

**Успешный ответ (200):**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_at": "2024-01-15 10:30:00",
    "worker": {
      "id": 1,
      "phone": "+998901234567",
      "full_name": "Test Worker",
      // ... другие поля профиля
    }
  },
  "message": "Successfully authenticated"
}
```

**Ошибки:**
- `422` - Не указан номер телефона или код
- `400` - Неверный или истекший код валидации
- `404` - Worker с таким номером не найден

## Процесс аутентификации

### Шаг 1: Запрос кода
```
Клиент -> POST /worker/auth/send-code
       -> phone: +998901234567

Сервер -> Генерирует 4-значный код
       -> Сохраняет код в кеше на 5 минут
       -> Отправляет SMS через eskiz.uz
       -> Сохраняет время отправки для ограничения повторных запросов
       -> Возвращает успешный ответ
```

### Шаг 2: Вход в систему
```
Клиент -> POST /worker/auth/login
       -> phone: +998901234567
       -> code: 1234

Сервер -> Проверяет код в кеше
       -> Удаляет код после успешной проверки
       -> Находит worker'а в базе данных
       -> Генерирует JWT токен на 7 дней
       -> Логирует вход в систему
       -> Возвращает токен и профиль пользователя
```

## Безопасность

1. **Ограничение частоты запросов:** Код можно запросить не чаще чем раз в 2 минуты
2. **Время жизни кода:** Код действителен только 5 минут
3. **Одноразовое использование:** Код удаляется после успешной проверки
4. **Токен доступа:** JWT токен действителен 7 дней

## Логирование

Все события аутентификации логируются через `LoggingService`:
- Успешные входы в систему
- Неудачные попытки входа
- Обновление токенов

## Интеграция с eskiz.uz

SMS отправляются через API eskiz.uz:
- **URL:** `https://notify.eskiz.uz/api`
- **Авторизация:** По номеру телефона
- **Формат сообщения:** "Sizning Ishchi hisobingizga kirish uchun tasdiqlash kodi: Kod: XXXX"

## Тестирование

Для тестирования API можно использовать:
1. `test_sms_api.php` - тестирование SMS сервиса
2. `test_api_endpoints.php` - тестирование HTTP endpoints
3. Postman/curl для ручного тестирования
