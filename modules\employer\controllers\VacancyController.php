<?php

namespace app\modules\employer\controllers;

use yii\web\Controller;

/**
 * Контроллер управления вакансиями
 */
class VacancyController extends Controller
{
    /**
     * Список вакансий
     * @return string
     */
    public function actionIndex()
    {
        return $this->render('index');
    }

    /**
     * Создание вакансии
     * @return string
     */
    public function actionCreate()
    {
        return $this->render('create');
    }

    /**
     * Редактирование вакансии
     * @param int $id
     * @return string
     */
    public function actionEdit($id)
    {
        return $this->render('edit', ['id' => $id]);
    }
} 