<?php
/**
 * Тестовый скрипт для проверки нового потока аутентификации
 */

require_once 'vendor/autoload.php';

// Подключаем Yii
defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'dev');

require 'vendor/yiisoft/yii2/Yii.php';

$config = require 'config/web.php';
new yii\web\Application($config);

use app\modules\worker\services\WorkerService;
use app\modules\worker\models\Worker;

echo "=== Тестирование нового потока аутентификации ===\n\n";

try {
    $workerService = new WorkerService();
    $testPhone = '+998901234567';
    
    echo "1. Проверка существования worker'а...\n";
    $exists = $workerService->workerExists($testPhone);
    echo "   Существует: " . ($exists ? 'Да' : 'Нет') . "\n\n";
    
    echo "2. Тестирование getOrCreateWorker...\n";
    $worker = $workerService->getOrCreateWorker($testPhone);
    
    if ($worker) {
        echo "   Worker получен/создан успешно\n";
        echo "   ID: {$worker->id}\n";
        echo "   Phone: {$worker->phone}\n";
        echo "   Profile Status: {$worker->profile_status}\n";
        echo "   Is Profile Complete: " . ($worker->isProfileComplete() ? 'Да' : 'Нет') . "\n";
        echo "   Is Minimal Profile: " . ($worker->isMinimalProfile() ? 'Да' : 'Нет') . "\n\n";
        
        echo "3. Тестирование форматирования профиля...\n";
        $profile = $workerService->formatWorkerProfile($worker);
        echo "   Profile Status: " . $profile['profile_status'] . "\n";
        echo "   Is Profile Complete: " . ($profile['is_profile_complete'] ? 'Да' : 'Нет') . "\n";
        echo "   Is Minimal Profile: " . ($profile['is_minimal_profile'] ? 'Да' : 'Нет') . "\n\n";
        
        echo "4. Тестирование обновления профиля...\n";
        $updateData = [
            'name' => 'Test Worker',
            'age' => 25,
            'about' => 'Test description',
            'profession_ids' => [1] // Предполагаем, что профессия с ID 1 существует
        ];
        
        $updatedProfile = $workerService->updateWorkerProfile($testPhone, $updateData);
        if ($updatedProfile) {
            echo "   Профиль обновлен успешно\n";
            echo "   Profile Status: " . $updatedProfile['profile_status'] . "\n";
            echo "   Is Profile Complete: " . ($updatedProfile['is_profile_complete'] ? 'Да' : 'Нет') . "\n\n";
        } else {
            echo "   Ошибка обновления профиля\n\n";
        }
        
        echo "5. Очистка тестовых данных...\n";
        // Помечаем как удаленного для очистки
        $worker->deleted_at = date('Y-m-d H:i:s');
        $worker->save(false);
        echo "   Тестовый worker помечен как удаленный\n";
        
    } else {
        echo "   Ошибка создания worker'а\n";
    }
    
    echo "\n=== Тест завершен ===\n";
    
} catch (Exception $e) {
    echo "ОШИБКА: " . $e->getMessage() . "\n";
    echo "Трассировка:\n" . $e->getTraceAsString() . "\n";
}
