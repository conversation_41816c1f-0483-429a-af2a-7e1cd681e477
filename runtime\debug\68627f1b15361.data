a:14:{s:6:"config";s:1745:"a:5:{s:10:"phpVersion";s:6:"8.1.31";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:6:"8.1.31";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:6:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:54:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:64:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:3036:"a:1:{s:8:"messages";a:13:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1751285531.079106;i:4;a:0:{}i:5;i:2591200;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1751285531.079743;i:4;a:0:{}i:5;i:2769096;}i:2;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1751285531.07975;i:4;a:0:{}i:5;i:2769896;}i:3;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1751285531.086551;i:4;a:0:{}i:5;i:4011584;}i:4;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1751285531.088692;i:4;a:0:{}i:5;i:4375488;}i:5;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1751285531.088889;i:4;a:0:{}i:5;i:4400320;}i:16;a:6:{i:0;s:40:"Route requested: 'worker/auth/send-code'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1751285531.090214;i:4;a:0:{}i:5;i:4695760;}i:17;a:6:{i:0;s:22:"Loading module: worker";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1751285531.09022;i:4;a:0:{}i:5;i:4698024;}i:18;a:6:{i:0;s:35:"Route to run: worker/auth/send-code";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1751285531.092277;i:4;a:0:{}i:5;i:5006136;}i:19;a:6:{i:0;s:39:"Rate limit skipped: user not logged in.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:1751285531.093358;i:4;a:0:{}i:5;i:5221584;}i:20;a:6:{i:0;s:79:"Running action: app\modules\worker\controllers\AuthController::actionSendCode()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1751285531.093391;i:4;a:0:{}i:5;i:5222856;}i:21;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1751285531.10296;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"D:\OSPanel\domains\ish_top\modules\worker\services\WorkerService.php";s:4:"line";i:198;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\worker\controllers\AuthController.php";s:4:"line";i:66;s:8:"function";s:12:"workerExists";s:5:"class";s:41:"app\modules\worker\services\WorkerService";s:4:"type";s:2:"->";}}i:5;i:6945208;}i:24;a:6:{i:0;s:97:"SELECT EXISTS(SELECT * FROM "workers" WHERE ("phone"='+998901234567') AND ("deleted_at" IS NULL))";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1751285531.134629;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"D:\OSPanel\domains\ish_top\modules\worker\services\WorkerService.php";s:4:"line";i:198;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\worker\controllers\AuthController.php";s:4:"line";i:66;s:8:"function";s:12:"workerExists";s:5:"class";s:41:"app\modules\worker\services\WorkerService";s:4:"type";s:2:"->";}}i:5;i:6947808;}}}";s:9:"profiling";s:2557:"a:3:{s:6:"memory";i:7330920;s:4:"time";d:0.07076883316040039;s:8:"messages";a:4:{i:22;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1751285531.102972;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"D:\OSPanel\domains\ish_top\modules\worker\services\WorkerService.php";s:4:"line";i:198;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\worker\controllers\AuthController.php";s:4:"line";i:66;s:8:"function";s:12:"workerExists";s:5:"class";s:41:"app\modules\worker\services\WorkerService";s:4:"type";s:2:"->";}}i:5;i:6946712;}i:23;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1751285531.134566;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"D:\OSPanel\domains\ish_top\modules\worker\services\WorkerService.php";s:4:"line";i:198;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\worker\controllers\AuthController.php";s:4:"line";i:66;s:8:"function";s:12:"workerExists";s:5:"class";s:41:"app\modules\worker\services\WorkerService";s:4:"type";s:2:"->";}}i:5;i:6949072;}i:25;a:6:{i:0;s:97:"SELECT EXISTS(SELECT * FROM "workers" WHERE ("phone"='+998901234567') AND ("deleted_at" IS NULL))";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1751285531.134954;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"D:\OSPanel\domains\ish_top\modules\worker\services\WorkerService.php";s:4:"line";i:198;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\worker\controllers\AuthController.php";s:4:"line";i:66;s:8:"function";s:12:"workerExists";s:5:"class";s:41:"app\modules\worker\services\WorkerService";s:4:"type";s:2:"->";}}i:5;i:6950928;}i:26;a:6:{i:0;s:97:"SELECT EXISTS(SELECT * FROM "workers" WHERE ("phone"='+998901234567') AND ("deleted_at" IS NULL))";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1751285531.141871;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"D:\OSPanel\domains\ish_top\modules\worker\services\WorkerService.php";s:4:"line";i:198;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\worker\controllers\AuthController.php";s:4:"line";i:66;s:8:"function";s:12:"workerExists";s:5:"class";s:41:"app\modules\worker\services\WorkerService";s:4:"type";s:2:"->";}}i:5;i:6952536;}}}";s:2:"db";s:1301:"a:1:{s:8:"messages";a:2:{i:25;a:6:{i:0;s:97:"SELECT EXISTS(SELECT * FROM "workers" WHERE ("phone"='+998901234567') AND ("deleted_at" IS NULL))";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1751285531.134954;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"D:\OSPanel\domains\ish_top\modules\worker\services\WorkerService.php";s:4:"line";i:198;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\worker\controllers\AuthController.php";s:4:"line";i:66;s:8:"function";s:12:"workerExists";s:5:"class";s:41:"app\modules\worker\services\WorkerService";s:4:"type";s:2:"->";}}i:5;i:6950928;}i:26;a:6:{i:0;s:97:"SELECT EXISTS(SELECT * FROM "workers" WHERE ("phone"='+998901234567') AND ("deleted_at" IS NULL))";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1751285531.141871;i:4;a:2:{i:0;a:5:{s:4:"file";s:68:"D:\OSPanel\domains\ish_top\modules\worker\services\WorkerService.php";s:4:"line";i:198;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"D:\OSPanel\domains\ish_top\modules\worker\controllers\AuthController.php";s:4:"line";i:66;s:8:"function";s:12:"workerExists";s:5:"class";s:41:"app\modules\worker\services\WorkerService";s:4:"type";s:2:"->";}}i:5;i:6952536;}}}";s:5:"event";s:2351:"a:13:{i:0;a:5:{s:4:"time";d:1751285531.090003;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1751285531.092349;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1751285531.092355;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\worker\Module";}i:3;a:5:{s:4:"time";d:1751285531.093384;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"app\modules\worker\controllers\AuthController";}i:4;a:5:{s:4:"time";d:1751285531.098139;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:5;a:5:{s:4:"time";d:1751285531.134557;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:6;a:5:{s:4:"time";d:1751285531.142687;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"app\modules\worker\controllers\AuthController";}i:7;a:5:{s:4:"time";d:1751285531.14294;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\worker\Module";}i:8;a:5:{s:4:"time";d:1751285531.142945;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:9;a:5:{s:4:"time";d:1751285531.142949;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:10;a:5:{s:4:"time";d:1751285531.142953;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:11;a:5:{s:4:"time";d:1751285531.144525;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:12;a:5:{s:4:"time";d:1751285531.144546;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1751285531.074932;s:3:"end";d:1751285531.145727;s:6:"memory";i:7330920;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:2066:"a:3:{s:8:"messages";a:10:{i:6;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1751285531.090178;i:4;a:0:{}i:5;i:4689152;}i:7;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1751285531.090185;i:4;a:0:{}i:5;i:4689904;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1751285531.090189;i:4;a:0:{}i:5;i:4690656;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1751285531.090191;i:4;a:0:{}i:5;i:4691728;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1751285531.090194;i:4;a:0:{}i:5;i:4692480;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:16:"telegram/webhook";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1751285531.090196;i:4;a:0:{}i:5;i:4693232;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:34:"telegram/registration/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1751285531.090198;i:4;a:0:{}i:5;i:4693984;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:32:"telegram/profession/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1751285531.0902;i:4;a:0:{}i:5;i:4694736;}i:14;a:6:{i:0;s:51:"Request parsed with URL rule: worker/auth/send-code";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1751285531.090207;i:4;a:0:{}i:5;i:4696024;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST worker/auth/send-code";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1751285531.09021;i:4;a:0:{}i:5;i:4696376;}}s:5:"route";s:21:"worker/auth/send-code";s:6:"action";s:63:"app\modules\worker\controllers\AuthController::actionSendCode()";}";s:7:"request";s:2060:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:404;s:14:"requestHeaders";a:4:{s:4:"host";s:14:"localhost:8000";s:6:"accept";s:3:"*/*";s:14:"content-length";s:2:"21";s:12:"content-type";s:33:"application/x-www-form-urlencoded";}s:15:"responseHeaders";a:6:{s:12:"X-Powered-By";s:10:"PHP/8.1.31";s:32:"Access-Control-Allow-Credentials";s:5:"false";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68627f1b15361";s:16:"X-Debug-Duration";s:2:"71";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=68627f1b15361";}s:5:"route";s:21:"worker/auth/send-code";s:6:"action";s:63:"app\modules\worker\controllers\AuthController::actionSendCode()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:33:"application/x-www-form-urlencoded";s:3:"Raw";s:21:"phone=%2B998901234567";s:7:"Decoded";a:1:{s:5:"phone";s:13:"+998901234567";}}s:6:"SERVER";a:21:{s:13:"DOCUMENT_ROOT";s:30:"D:\OSPanel\domains\ish_top\web";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:11:"REMOTE_PORT";s:5:"62345";s:15:"SERVER_SOFTWARE";s:29:"PHP 8.1.31 Development Server";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SERVER_PORT";s:4:"8000";s:11:"REQUEST_URI";s:22:"/worker/auth/send-code";s:14:"REQUEST_METHOD";s:4:"POST";s:11:"SCRIPT_NAME";s:10:"/index.php";s:15:"SCRIPT_FILENAME";s:40:"D:\OSPanel\domains\ish_top\web\index.php";s:9:"PATH_INFO";s:22:"/worker/auth/send-code";s:8:"PHP_SELF";s:32:"/index.php/worker/auth/send-code";s:9:"HTTP_HOST";s:14:"localhost:8000";s:11:"HTTP_ACCEPT";s:3:"*/*";s:14:"CONTENT_LENGTH";s:2:"21";s:19:"HTTP_CONTENT_LENGTH";s:2:"21";s:12:"CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:17:"HTTP_CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:18:"REQUEST_TIME_FLOAT";d:1751285531.074111;s:12:"REQUEST_TIME";i:1751285531;}s:3:"GET";a:0:{}s:4:"POST";a:1:{s:5:"phone";s:13:"+998901234567";}s:6:"COOKIE";a:0:{}s:5:"FILES";a:0:{}s:7:"SESSION";a:0:{}}";s:4:"user";s:2:"N;";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"68627f1b15361";s:3:"url";s:43:"http://localhost:8000/worker/auth/send-code";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751285531.074111;s:10:"statusCode";i:404;s:8:"sqlCount";i:1;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7330920;s:14:"processingTime";d:0.07076883316040039;}s:10:"exceptions";a:0:{}}