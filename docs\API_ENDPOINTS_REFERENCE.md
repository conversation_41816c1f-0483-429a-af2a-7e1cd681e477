# Справочник API Endpoints

## Краткая справка по изменениям в API

### Endpoints аутентификации

| Endpoint | Метод | Изменения | Новые поля в ответе |
|----------|-------|-----------|-------------------|
| `/worker/auth/send-code` | POST | ✅ Автоматическая регистрация | `is_new_registration`, `registration_info` |
| `/worker/auth/login` | POST | ✅ Информация о профиле | `profile_completion_required`, `profile_completion_message` |
| `/worker/auth/logout` | POST | Без изменений | - |
| `/worker/auth/verify` | GET | Без изменений | - |
| `/worker/auth/refresh` | POST | Без изменений | - |

### Endpoints профиля

| Endpoint | Метод | Требует завершенного профиля | Изменения |
|----------|-------|----------------------------|-----------|
| `/worker/profile/index` | GET | ❌ Нет | ✅ Новые поля статуса |
| `/worker/profile/update` | PUT | ❌ Нет | ✅ Автоматическое завершение |
| `/worker/profile/upload-audio` | POST | ❌ Нет | Без изменений |
| `/worker/profile/add-favorite` | POST | ✅ Да | ✅ Проверка профиля |
| `/worker/profile/remove-favorite` | POST | ✅ Да | ✅ Проверка профиля |
| `/worker/profile/favorites` | GET | ✅ Да | ✅ Проверка профиля |

### Endpoints вакансий

| Endpoint | Метод | Требует завершенного профиля | Изменения |
|----------|-------|----------------------------|-----------|
| `/worker/vacancy/list` | GET | ✅ Да | ✅ Проверка профиля |
| `/worker/vacancy/search` | GET | ✅ Да | ✅ Проверка профиля |
| `/worker/vacancy/detail/{id}` | GET | ✅ Да | ✅ Проверка профиля |

---

## Новые поля в ответах API

### Поля профиля worker'а

```json
{
  "worker": {
    // ... существующие поля ...
    "profile_status": "incomplete|complete",
    "is_profile_complete": true|false,
    "is_minimal_profile": true|false
  }
}
```

### Поля при отправке кода (send-code)

```json
{
  "data": {
    // ... существующие поля ...
    "is_new_registration": true|false,
    "profile_status": "incomplete|complete",
    "registration_info": {  // только для новых пользователей
      "message": "Welcome! Please complete your profile after login",
      "telegram_bot_url": "https://t.me/your_bot_name",
      "requires_profile_completion": true
    }
  }
}
```

### Поля при логине

```json
{
  "data": {
    // ... существующие поля ...
    "profile_completion_required": true|false,
    "profile_completion_message": "Please complete your profile to access all features"
  }
}
```

### Ошибка незавершенного профиля (403)

```json
{
  "success": false,
  "data": {
    "profile_completion_required": true,
    "message": "Please complete your profile to access this feature",
    "profile_status": "incomplete",
    "completion_steps": {
      "name": true|false,
      "age": true|false,
      "about": true|false,
      "professions": true|false
    }
  },
  "message": "Profile completion required"
}
```

---

## Логика определения типа пользователя

### Алгоритм обработки в send-code

```
1. Получить номер телефона
2. Проверить ограничение на повторную отправку (2 минуты)
3. Вызвать WorkerService::getOrCreateWorker()
   ├── Если worker существует → вернуть существующего
   └── Если НЕ существует → создать минимальную запись
4. Определить is_new_registration = worker.isMinimalProfile()
5. Отправить SMS код
6. Вернуть ответ с информацией о типе пользователя
```

### Алгоритм проверки профиля в endpoints

```
1. Получить текущего worker'а из токена
2. Проверить action.id в списке requiresProfileCompletion()
3. Если требуется завершенный профиль:
   ├── Если worker.isProfileComplete() → разрешить доступ
   └── Если НЕ завершен → вернуть 403 с completion_steps
4. Если НЕ требуется → разрешить доступ
```

### Алгоритм автоматического завершения профиля

```
При обновлении профиля (profile/update):
1. Сохранить изменения
2. Проверить заполненность обязательных полей:
   - name (не пустое)
   - age (не пустое)
   - about (не пустое)
   - professions (массив не пустой)
3. Если ВСЕ поля заполнены:
   └── Автоматически установить profile_status = "complete"
```

---

## Константы и перечисления

### Статусы профиля

```php
Worker::PROFILE_STATUS_INCOMPLETE = 'incomplete'
Worker::PROFILE_STATUS_COMPLETE = 'complete'
```

### HTTP статусы

| Статус | Описание | Когда возвращается |
|--------|----------|-------------------|
| 200 | OK | Успешная операция |
| 400 | Bad Request | Неверный код валидации, отсутствуют параметры |
| 401 | Unauthorized | Неверный/истекший токен |
| 403 | Forbidden | Незавершенный профиль для защищенного endpoint |
| 404 | Not Found | Ресурс не найден |
| 422 | Unprocessable Entity | Ошибки валидации |
| 429 | Too Many Requests | Слишком частые запросы SMS кода |
| 500 | Internal Server Error | Ошибка сервера, ошибка отправки SMS |

---

## Миграция существующих приложений

### Что нужно обновить в клиентских приложениях

1. **Обработка новых полей в ответах API**
   - `is_new_registration` в send-code
   - `profile_completion_required` в login
   - Новые поля профиля worker'а

2. **Обработка новых HTTP статусов**
   - 403 для незавершенного профиля
   - 429 для частых запросов SMS

3. **Добавление экранов завершения профиля**
   - Форма заполнения обязательных полей
   - Индикатор прогресса завершения

4. **Обновление логики навигации**
   - Проверка статуса профиля после логина
   - Перенаправление на завершение профиля

### Обратная совместимость

✅ **Сохранена полная обратная совместимость:**
- Все существующие endpoints работают как раньше
- Новые поля добавлены, старые не изменены
- Существующие пользователи не затронуты
- Telegram бот продолжает работать без изменений

### Рекомендации по внедрению

1. **Поэтапное внедрение:**
   - Сначала добавить обработку новых полей
   - Затем добавить экраны завершения профиля
   - В конце добавить обработку ошибок 403

2. **Тестирование:**
   - Протестировать с существующими пользователями
   - Протестировать с новыми номерами телефонов
   - Проверить все сценарии завершения профиля

3. **Мониторинг:**
   - Отслеживать количество новых регистраций
   - Мониторить процент завершения профилей
   - Анализировать ошибки 403 для оптимизации UX
