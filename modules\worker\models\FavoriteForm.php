<?php

namespace app\modules\worker\models;

use yii\base\Model;
use app\modules\employer\models\Vacancy;
use app\common\enums\VacancyStatus;

/**
 * Форм-модель для работы с избранными вакансиями
 */
class FavoriteForm extends Model
{
    public $vacancy_id;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['vacancy_id'], 'required'],
            [['vacancy_id'], 'integer', 'min' => 1],
            [['vacancy_id'], 'validateVacancyExists'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'vacancy_id' => \Yii::t('app', 'worker.vacancy_id'),
        ];
    }

    /**
     * Валидация существования активной вакансии
     */
    public function validateVacancyExists($attribute, $params)
    {
        if (!empty($this->vacancy_id)) {
            $vacancy = Vacancy::findOne([
                'id' => $this->vacancy_id,
                'deleted_at' => null,
                'status' => VacancyStatus::ACTIVE->value
            ]);

            if (!$vacancy) {
                $this->addError($attribute, \Yii::t('app', 'worker.vacancy_not_found'));
            }
        }
    }

    /**
     * Загрузить данные из POST запроса
     */
    public function loadFromRequest($request)
    {
        $this->vacancy_id = (int) $request->post('vacancy_id');
        return $this->validate();
    }

    /**
     * Получить объект вакансии
     */
    public function getVacancy()
    {
        if (!$this->vacancy_id) {
            return null;
        }

        return Vacancy::findOne([
            'id' => $this->vacancy_id,
            'deleted_at' => null,
            'status' => VacancyStatus::ACTIVE->value
        ]);
    }
} 