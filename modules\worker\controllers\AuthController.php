<?php

namespace app\modules\worker\controllers;

use app\modules\worker\models\Worker;
use app\modules\worker\services\WorkerService;
use app\modules\worker\services\LoggingService;
use app\common\services\ApiResponse;
use app\common\services\SmsService;
use yii\web\Response;

/**
 * Контроллер авторизации для модуля worker
 */
class AuthController extends BaseApiController
{
    /**
     * @var WorkerService
     */
    private $workerService;

    /**
     * @var LoggingService
     */
    private $loggingService;

    /**
     * @var SmsService
     */
    private $smsService;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        $this->workerService = new WorkerService();
        $this->loggingService = new LoggingService();
        $this->smsService = new SmsService();
    }

    /**
     * Отправка кода валидации на номер телефона
     *
     * POST /worker/auth/send-code
     *
     * @return Response
     */
    public function actionSendCode()
    {
        $request = \Yii::$app->request;
        $phone = $request->post('phone');

        // Валидация входных данных
        if (empty($phone)) {
            return $this->sendValidationError([
                'phone' => [$this->t('app', 'Phone number is required')]
            ]);
        }

        // Нормализация номера телефона
        $phone = $this->normalizePhone($phone);

        // Проверяем ограничение на повторную отправку (2 минуты)
        $lastSentKey = 'sms_last_sent_' . md5($phone);
        $lastSentTime = \Yii::$app->cache->get($lastSentKey);

        if ($lastSentTime && (time() - $lastSentTime) < 120) { // 120 секунд = 2 минуты
            $remainingTime = 120 - (time() - $lastSentTime);
            return $this->sendError(
                $this->t('app', 'Please wait {seconds} seconds before requesting a new code', [
                    'seconds' => $remainingTime
                ]),
                ['remaining_seconds' => $remainingTime],
                429 // Too Many Requests
            );
        }

        // Получаем или создаем работника
        $worker = $this->workerService->getOrCreateWorker($phone);

        if (!$worker) {
            return $this->sendError(
                $this->t('app', 'Failed to process worker registration'),
                null,
                500
            );
        }

        // Определяем, является ли это новой регистрацией
        $isNewRegistration = $worker->isMinimalProfile();

        // Генерируем код валидации
        $code = $this->smsService->generateValidationCode(4);

        // Сохраняем код в кеше на 5 минут
        $this->smsService->saveValidationCode($phone, $code, 300);

        // Отправляем SMS с кодом
        $smsResult = $this->smsService->sendValidationCode($phone, $code, 'worker');

        if ($smsResult['success']) {
            // Сохраняем время отправки для ограничения повторных запросов
            \Yii::$app->cache->set($lastSentKey, time(), 120); // 2 минуты

            $responseData = [
                'message' => $this->t('app', 'Validation code sent to your phone'),
                'phone' => $phone,
                'expires_in' => 300, // 5 минут
                'next_request_in' => 120, // 2 минуты до следующего запроса
                'is_new_registration' => $isNewRegistration,
                'profile_status' => $worker->profile_status
            ];

            // Добавляем дополнительную информацию для новых пользователей
            if ($isNewRegistration) {
                $responseData['registration_info'] = [
                    'message' => $this->t('app', 'Welcome! Please complete your profile after login'),
                    'telegram_bot_url' => 'https://t.me/your_bot_name', // Замените на ваш бот
                    'requires_profile_completion' => true
                ];
            }

            return $this->sendSuccess($responseData, $this->t('app', 'Code sent successfully'));
        } else {
            return $this->sendError(
                $this->t('app', 'Failed to send SMS'),
                $smsResult,
                500
            );
        }
    }

    /**
     * Авторизация по номеру телефона и коду валидации
     *
     * POST /worker/auth/login
     *
     * @return Response
     */
    public function actionLogin()
    {
        $request = \Yii::$app->request;
        $phone = $request->post('phone');
        $code = $request->post('code');

        // Валидация входных данных
        if (empty($phone)) {
            return $this->sendValidationError([
                'phone' => [$this->t('app', 'Phone number is required')]
            ]);
        }

        if (empty($code)) {
            return $this->sendValidationError([
                'code' => [$this->t('app', 'Validation code is required')]
            ]);
        }

        // Нормализация номера телефона
        $phone = $this->normalizePhone($phone);

        // Проверяем код валидации
        if (!$this->smsService->verifyValidationCode($phone, $code)) {
            return $this->sendError(
                $this->t('app', 'Invalid or expired validation code'),
                null,
                400
            );
        }

        // Проверяем существование работника
        if (!$this->workerService->workerExists($phone)) {
            return $this->sendError(
                $this->t('app', 'Worker with this phone number not found'),
                null,
                404
            );
        }

        // Находим работника
        $worker = Worker::findOne(['phone' => $phone, 'deleted_at' => null]);

        if (!$worker) {
            return $this->sendError(
                $this->t('app', 'Worker not found'),
                null,
                404
            );
        }

        // Генерируем новый токен
        $token = $worker->generateAuthToken(24 * 7); // Токен на неделю

        // Логируем авторизацию
        $this->loggingService->logLogin($worker, $phone, $worker->token_expires_at);

        $workerProfile = $this->workerService->getWorkerProfileById($worker->id);

        $responseData = [
            'access_token' => $token,
            'token_type' => 'Bearer',
            'expires_at' => $worker->token_expires_at,
            'worker' => $workerProfile
        ];

        // Добавляем предупреждение для неполных профилей
        if (!$worker->isProfileComplete()) {
            $responseData['profile_completion_required'] = true;
            $responseData['profile_completion_message'] = $this->t('app', 'Please complete your profile to access all features');
        }

        return $this->sendSuccess($responseData, $this->t('app', 'Successfully authenticated'));
    }

    /**
     * Выход из системы (очистка токена)
     * 
     * POST /worker/auth/logout
     * 
     * @return Response
     */
    public function actionLogout()
    {
        $token = $this->getTokenFromRequest();
        
        if (empty($token)) {
            return $this->sendError($this->t('app', 'Token not provided'));
        }

        $worker = Worker::findByAuthToken($token);
        
        if ($worker) {
            // Логируем выход
            $this->loggingService->logLogout($worker);

            // Очищаем токен
            $worker->clearAuthToken();
        }

        return $this->sendSuccess(null, $this->t('app', 'Successfully logged out'));
    }

    /**
     * Проверка валидности токена
     * 
     * GET /worker/auth/verify
     * 
     * @return Response
     */
    public function actionVerify()
    {
        $token = $this->getTokenFromRequest();
        
        if (empty($token)) {
            return $this->sendUnauthorized($this->t('app', 'Token not provided'));
        }

        $worker = Worker::findByAuthToken($token);
        
        if (!$worker) {
            return $this->sendUnauthorized($this->t('app', 'Invalid or expired token'));
        }

        return $this->sendSuccess([
            'valid' => true,
            'expires_at' => $worker->token_expires_at,
            'worker' => $this->workerService->getWorkerProfileById($worker->id)
        ], $this->t('app', 'Token is valid'));
    }

    /**
     * Обновление токена
     * 
     * POST /worker/auth/refresh
     * 
     * @return Response
     */
    public function actionRefresh()
    {
        $token = $this->getTokenFromRequest();
        
        if (empty($token)) {
            return $this->sendUnauthorized($this->t('app', 'Token not provided'));
        }

        $worker = Worker::findByAuthToken($token);
        
        if (!$worker) {
            return $this->sendUnauthorized($this->t('app', 'Invalid or expired token'));
        }

        // Генерируем новый токен
        $newToken = $worker->generateAuthToken(24 * 7); // Токен на неделю

        // Логируем обновление токена
        $this->loggingService->logTokenRefresh($worker, $token, $worker->token_expires_at);

        return $this->sendSuccess([
            'access_token' => $newToken,
            'token_type' => 'Bearer',
            'expires_at' => $worker->token_expires_at
        ], $this->t('app', 'Token refreshed successfully'));
    }

    /**
     * Получить токен из запроса
     * 
     * @return string|null
     */
    private function getTokenFromRequest()
    {
        $request = \Yii::$app->request;

        // Проверяем заголовок Authorization
        $authHeader = $request->headers->get('Authorization');
        if ($authHeader && preg_match('/^Bearer\s+(.+)$/', $authHeader, $matches)) {
            return $matches[1];
        }

        // Проверяем параметр access_token
        $accessToken = $request->get('access_token') ?: $request->post('access_token');
        if ($accessToken) {
            return $accessToken;
        }

        return null;
    }

    /**
     * Нормализация номера телефона
     * 
     * @param string $phone
     * @return string
     */
    private function normalizePhone($phone)
    {
        // Удаляем все символы кроме цифр и +
        $phone = preg_replace('/[^\d+]/', '', $phone);
        
        // Если номер не начинается с +, добавляем +998 (Узбекистан)
        if (strpos($phone, '+') !== 0) {
            $phone = '+998' . $phone;
        }
        
        return $phone;
    }
}
