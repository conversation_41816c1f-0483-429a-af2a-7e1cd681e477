<?php

namespace app\modules\worker\controllers;

use app\modules\worker\models\Worker;
use app\modules\worker\services\WorkerService;
use app\modules\worker\services\LoggingService;
use app\common\services\ApiResponse;
use yii\web\Response;

/**
 * Контроллер авторизации для модуля worker
 */
class AuthController extends BaseApiController
{
    /**
     * @var WorkerService
     */
    private $workerService;

    /**
     * @var LoggingService
     */
    private $loggingService;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        $this->workerService = new WorkerService();
        $this->loggingService = new LoggingService();
    }

    /**
     * Авторизация по номеру телефона
     * 
     * POST /worker/auth/login
     * 
     * @return Response
     */
    public function actionLogin()
    {
        $request = \Yii::$app->request;
        $phone = $request->post('phone');

        // Валидация входных данных
        if (empty($phone)) {
            return $this->sendValidationError([
                'phone' => [$this->t('app', 'Phone number is required')]
            ]);
        }

        // Нормализация номера телефона
        $phone = $this->normalizePhone($phone);

        // Проверяем существование работника
        if (!$this->workerService->workerExists($phone)) {
            return $this->sendError(
                $this->t('app', 'Worker with this phone number not found'),
                null,
                404
            );
        }

        // Находим работника
        $worker = Worker::findOne(['phone' => $phone, 'deleted_at' => null]);
        
        if (!$worker) {
            return $this->sendError(
                $this->t('app', 'Worker not found'),
                null,
                404
            );
        }

        // Генерируем новый токен
        $token = $worker->generateAuthToken(24 * 7); // Токен на неделю

        // Логируем авторизацию
        $this->loggingService->logLogin($worker, $phone, $worker->token_expires_at);

        return $this->sendSuccess([
            'access_token' => $token,
            'token_type' => 'Bearer',
            'expires_at' => $worker->token_expires_at,
            'worker' => $this->workerService->getWorkerProfileById($worker->id)
        ], $this->t('app', 'Successfully authenticated'));
    }

    /**
     * Выход из системы (очистка токена)
     * 
     * POST /worker/auth/logout
     * 
     * @return Response
     */
    public function actionLogout()
    {
        $token = $this->getTokenFromRequest();
        
        if (empty($token)) {
            return $this->sendError($this->t('app', 'Token not provided'));
        }

        $worker = Worker::findByAuthToken($token);
        
        if ($worker) {
            // Логируем выход
            $this->loggingService->logLogout($worker);

            // Очищаем токен
            $worker->clearAuthToken();
        }

        return $this->sendSuccess(null, $this->t('app', 'Successfully logged out'));
    }

    /**
     * Проверка валидности токена
     * 
     * GET /worker/auth/verify
     * 
     * @return Response
     */
    public function actionVerify()
    {
        $token = $this->getTokenFromRequest();
        
        if (empty($token)) {
            return $this->sendUnauthorized($this->t('app', 'Token not provided'));
        }

        $worker = Worker::findByAuthToken($token);
        
        if (!$worker) {
            return $this->sendUnauthorized($this->t('app', 'Invalid or expired token'));
        }

        return $this->sendSuccess([
            'valid' => true,
            'expires_at' => $worker->token_expires_at,
            'worker' => $this->workerService->getWorkerProfileById($worker->id)
        ], $this->t('app', 'Token is valid'));
    }

    /**
     * Обновление токена
     * 
     * POST /worker/auth/refresh
     * 
     * @return Response
     */
    public function actionRefresh()
    {
        $token = $this->getTokenFromRequest();
        
        if (empty($token)) {
            return $this->sendUnauthorized($this->t('app', 'Token not provided'));
        }

        $worker = Worker::findByAuthToken($token);
        
        if (!$worker) {
            return $this->sendUnauthorized($this->t('app', 'Invalid or expired token'));
        }

        // Генерируем новый токен
        $newToken = $worker->generateAuthToken(24 * 7); // Токен на неделю

        // Логируем обновление токена
        $this->loggingService->logTokenRefresh($worker, $token, $worker->token_expires_at);

        return $this->sendSuccess([
            'access_token' => $newToken,
            'token_type' => 'Bearer',
            'expires_at' => $worker->token_expires_at
        ], $this->t('app', 'Token refreshed successfully'));
    }

    /**
     * Получить токен из запроса
     * 
     * @return string|null
     */
    private function getTokenFromRequest()
    {
        $request = \Yii::$app->request;

        // Проверяем заголовок Authorization
        $authHeader = $request->headers->get('Authorization');
        if ($authHeader && preg_match('/^Bearer\s+(.+)$/', $authHeader, $matches)) {
            return $matches[1];
        }

        // Проверяем параметр access_token
        $accessToken = $request->get('access_token') ?: $request->post('access_token');
        if ($accessToken) {
            return $accessToken;
        }

        return null;
    }

    /**
     * Нормализация номера телефона
     * 
     * @param string $phone
     * @return string
     */
    private function normalizePhone($phone)
    {
        // Удаляем все символы кроме цифр и +
        $phone = preg_replace('/[^\d+]/', '', $phone);
        
        // Если номер не начинается с +, добавляем +998 (Узбекистан)
        if (strpos($phone, '+') !== 0) {
            $phone = '+998' . $phone;
        }
        
        return $phone;
    }
}
