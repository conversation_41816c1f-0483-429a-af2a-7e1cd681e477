<?php

namespace app\modules\worker\controllers;

use app\modules\worker\services\WorkerService;
use app\modules\worker\services\LoggingService;
use app\modules\worker\filters\AuthFilter;
use app\modules\worker\models\Worker;
use app\modules\worker\models\ProfileUpdateForm;
use app\modules\worker\models\AudioUploadForm;
use app\modules\worker\models\FavoriteForm;
use yii\web\Response;
use yii\web\UploadedFile;

/**
 * API контроллер профиля работника
 */
class ProfileController extends BaseApiController
{
    /**
     * @var WorkerService
     */
    private $workerService;

    /**
     * @var LoggingService
     */
    private $loggingService;

    /**
     * @var Worker|null Текущий авторизованный работник
     */
    public $currentWorker;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        $this->workerService = new WorkerService();
        $this->loggingService = new LoggingService();
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        // Все действия профиля требуют авторизации
        $behaviors['auth'] = [
            'class' => AuthFilter::class,
            'authRequired' => [
                'profile/index',
                'profile/update',
                'profile/upload-audio',
                'profile/add-favorite',
                'profile/remove-favorite',
                'profile/favorites'
            ]
        ];

        return $behaviors;
    }

    /**
     * Получить профиль работника
     *
     * GET /worker/profile
     *
     * @return Response
     */
    public function actionIndex()
    {
        if (!$this->currentWorker) {
            return $this->sendUnauthorized($this->t('app', 'Authentication required'));
        }

        try {
            $profile = $this->workerService->getWorkerProfileById($this->currentWorker->id);

            if (!$profile) {
                return $this->sendNotFound($this->t('app', 'Profile not found'));
            }

            // Логируем просмотр профиля
            $this->loggingService->logProfileView($this->currentWorker);

            return $this->sendSuccess(
                $profile,
                $this->t('app', 'Profile retrieved successfully')
            );
        } catch (\Exception $e) {
            \Yii::error('Error getting worker profile: ' . $e->getMessage(), 'worker-api');
            return $this->sendError($this->t('app', 'Error retrieving profile'));
        }
    }

    /**
     * Обновить профиль работника
     *
     * PUT /worker/profile
     *
     * @return Response
     */
    public function actionUpdate()
    {
        if (!$this->currentWorker) {
            return $this->sendUnauthorized($this->t('app', 'Authentication required'));
        }

        $form = new ProfileUpdateForm();
        $request = \Yii::$app->request;

        // Загружаем данные из запроса
        if (!$form->loadFromRequest($request)) {
            return $this->sendValidationError($form->errors);
        }

        // Валидируем форму
        if (!$form->validate()) {
            return $this->sendValidationError($form->errors);
        }

        try {
            $data = $form->getUpdateData();
            $result = $this->workerService->updateWorkerProfile($this->currentWorker->phone, $data);

            if ($result === false) {
                return $this->sendError($this->t('app', 'Failed to update profile'));
            }

            if (isset($result['errors'])) {
                return $this->sendValidationError($result['errors']);
            }

            // Логируем обновление профиля
            $this->loggingService->logProfileUpdate($this->currentWorker, array_keys($data));

            return $this->sendSuccess(
                $result,
                $this->t('app', 'Profile updated successfully')
            );
        } catch (\Exception $e) {
            \Yii::error('Error updating worker profile: ' . $e->getMessage(), 'worker-api');
            return $this->sendError($this->t('app', 'Error updating profile'));
        }
    }

    /**
     * Загрузить аудиофайл
     *
     * POST /worker/profile/upload-audio
     *
     * @return Response
     */
    public function actionUploadAudio()
    {
        if (!$this->currentWorker) {
            return $this->sendUnauthorized($this->t('app', 'Authentication required'));
        }

        $form = new AudioUploadForm();
        
        // Загружаем файл
        if (!$form->loadFile()) {
            return $this->sendValidationError([
                'audio' => [$this->t('app', 'Audio file is required')]
            ]);
        }

        // Валидируем форму
        if (!$form->validate()) {
            return $this->sendValidationError($form->errors);
        }

        try {
            $result = $this->workerService->uploadAudioFile($this->currentWorker->phone, $form->audio);

            if ($result === false) {
                return $this->sendError($this->t('app', 'Failed to upload audio file'));
            }

            if (isset($result['error'])) {
                return $this->sendValidationError([
                    'audio' => [$this->t('app', $result['error'])]
                ]);
            }

            // Логируем загрузку аудио
            $fileInfo = $form->getFileInfo();
            $fileInfo['size_formatted'] = $form->getFormattedFileSize();
            $this->loggingService->logAudioUpload($this->currentWorker, $fileInfo, $result['audio_url']);

            return $this->sendSuccess(
                $result,
                $this->t('app', 'Audio file uploaded successfully')
            );
        } catch (\Exception $e) {
            \Yii::error('Error uploading audio file: ' . $e->getMessage(), 'worker-api');
            return $this->sendError($this->t('app', 'Error uploading audio file'));
        }
    }



    /**
     * Добавить вакансию в избранное
     *
     * POST /worker/profile/add-favorite
     *
     * @return Response
     */
    public function actionAddFavorite()
    {
        if (!$this->currentWorker) {
            return $this->sendUnauthorized($this->t('app', 'Authentication required'));
        }

        $form = new FavoriteForm();
        $request = \Yii::$app->request;

        // Загружаем данные из запроса и валидируем
        if (!$form->loadFromRequest($request)) {
            return $this->sendValidationError($form->errors);
        }

        try {
            $result = $this->workerService->addToFavorites($this->currentWorker->id, $form->vacancy_id);

            if (!$result) {
                return $this->sendError($this->t('app', 'Failed to add vacancy to favorites'));
            }

            // Логируем добавление в избранное
            $this->loggingService->logVacancyFavorite($this->currentWorker, $form->vacancy_id, 'added');

            return $this->sendSuccess(
                ['vacancy_id' => $form->vacancy_id, 'is_favorite' => true],
                $this->t('app', 'Vacancy added to favorites successfully')
            );
        } catch (\Exception $e) {
            \Yii::error('Error adding vacancy to favorites: ' . $e->getMessage(), 'worker-api');
            return $this->sendError($this->t('app', 'Error adding to favorites'));
        }
    }

    /**
     * Удалить вакансию из избранного
     *
     * POST /worker/profile/remove-favorite
     *
     * @return Response
     */
    public function actionRemoveFavorite()
    {
        if (!$this->currentWorker) {
            return $this->sendUnauthorized($this->t('app', 'Authentication required'));
        }

        $form = new FavoriteForm();
        $request = \Yii::$app->request;

        // Загружаем данные из запроса и валидируем
        if (!$form->loadFromRequest($request)) {
            return $this->sendValidationError($form->errors);
        }

        try {
            $result = $this->workerService->removeFromFavorites($this->currentWorker->id, $form->vacancy_id);

            if (!$result) {
                return $this->sendError($this->t('app', 'Vacancy not found in favorites'));
            }

            // Логируем удаление из избранного
            $this->loggingService->logVacancyFavorite($this->currentWorker, $form->vacancy_id, 'removed');

            return $this->sendSuccess(
                ['vacancy_id' => $form->vacancy_id, 'is_favorite' => false],
                $this->t('app', 'Vacancy removed from favorites successfully')
            );
        } catch (\Exception $e) {
            \Yii::error('Error removing vacancy from favorites: ' . $e->getMessage(), 'worker-api');
            return $this->sendError($this->t('app', 'Error removing from favorites'));
        }
    }

    /**
     * Получить список избранных вакансий
     *
     * GET /worker/profile/favorites
     *
     * @return Response
     */
    public function actionFavorites()
    {
        if (!$this->currentWorker) {
            return $this->sendUnauthorized($this->t('app', 'Authentication required'));
        }

        $request = \Yii::$app->request;
        
        // Параметры пагинации
        $page = (int) $request->get('page', 1);
        $perPage = min((int) $request->get('per_page', 20), 50);

        try {
            $result = $this->workerService->getFavoriteVacancies($this->currentWorker->id, $page, $perPage);

            // Логируем просмотр избранных
            $this->loggingService->logFavoritesView($this->currentWorker, $result['total'], $page);

            return $this->sendPaginated(
                $result['items'],
                $result['total'],
                $result['page'],
                $result['per_page'],
                $this->t('app', 'Favorite vacancies retrieved successfully')
            );
        } catch (\Exception $e) {
            \Yii::error('Error getting favorite vacancies: ' . $e->getMessage(), 'worker-api');
            return $this->sendError($this->t('app', 'Error retrieving favorite vacancies'));
        }
    }
}