<?php
/**
 * Конфигурация маршрутов приложения
 */

$routes = [];

// ========================================
// Telegram маршруты (не трогаем)
// ========================================
$routes['telegram/webhook'] = 'telegram/webhook';
$routes['telegram/registration/<action:\w+>'] = 'telegram/registration/<action>';
$routes['telegram/profession/<action:\w+>'] = 'telegram/profession/<action>';


// ========================================
// Worker API маршруты (вручную)
// ========================================
// Auth
$routes['POST worker/auth/send-code'] = 'worker/auth/send-code';
$routes['POST worker/auth/login'] = 'worker/auth/login';
$routes['POST worker/auth/logout'] = 'worker/auth/logout';
$routes['GET worker/auth/verify'] = 'worker/auth/verify';
$routes['POST worker/auth/refresh'] = 'worker/auth/refresh';

// Vacancy
$routes['GET worker/vacancy/list'] = 'worker/vacancy/list';
$routes['GET worker/vacancy/search'] = 'worker/vacancy/search';
$routes['GET worker/vacancy/detail/<id:\d+>'] = 'worker/vacancy/detail';

// Profile
$routes['GET worker/profile/index'] = 'worker/profile/index';
$routes['PUT worker/profile/update'] = 'worker/profile/update';
$routes['POST worker/profile/upload-audio'] = 'worker/profile/upload-audio';

// ========================================
// Employer маршруты
// ========================================
$routes['employer/<action:\w+>'] = 'employer/default/<action>';
$routes['employer/vacancy/<action:\w+>'] = 'employer/vacancy/<action>';

// ========================================
// Основные маршруты приложения
// ========================================
$routes[''] = 'site/index';
$routes['<action:\w+>'] = 'site/<action>';

return $routes;
