# Руководство по API аутентификации Worker модуля

## Обзор

После внесенных изменений система аутентификации поддерживает два типа пользователей:
- **Существующие пользователи** - зарегистрированные через Telegram бота
- **Новые пользователи** - впервые использующие мобильное приложение

Система автоматически определяет тип пользователя и предоставляет соответствующий уровень доступа.

---

## Сценарий 1: Существующий пользователь (Telegram)

### Характеристики существующего пользователя:
- Уже зарегистрирован через Telegram бота
- Имеет заполненный профиль (имя, возраст, описание, профессии)
- Статус профиля: `complete`
- Полный доступ ко всем функциям

### Шаг 1: Запрос кода валидации

**HTTP запрос:**
```bash
curl -X POST http://localhost:8000/worker/auth/send-code \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "phone=+998901234567"
```

**Ответ сервера (200 OK):**
```json
{
  "success": true,
  "data": {
    "message": "Validation code sent to your phone",
    "phone": "+998901234567",
    "expires_in": 300,
    "next_request_in": 120,
    "is_new_registration": false,
    "profile_status": "complete"
  },
  "message": "Code sent successfully"
}
```

**Объяснение полей:**
- `expires_in`: Код действителен 5 минут (300 секунд)
- `next_request_in`: Следующий запрос кода возможен через 2 минуты
- `is_new_registration`: false - это существующий пользователь
- `profile_status`: "complete" - профиль полностью заполнен

### Шаг 2: Вход в систему с кодом

**HTTP запрос:**
```bash
curl -X POST http://localhost:8000/worker/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "phone=+998901234567" \
  -d "code=1234"
```

**Ответ сервера (200 OK):**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_at": "2024-01-22 10:30:00",
    "worker": {
      "id": 15,
      "name": "Иван Петров",
      "phone": "+998901234567",
      "age": 28,
      "experience_years": 5,
      "about": "Опытный строитель с 5-летним стажем",
      "language": "ru",
      "location": {
        "lat": 41.2995,
        "lng": 69.2401
      },
      "audio_file_url": "/uploads/audio/worker_15_1642845600.mp3",
      "professions": [
        {
          "id": 1,
          "name_ru": "Строитель",
          "name_uz": "Quruvchi",
          "name_en": "Builder"
        }
      ],
      "profile_status": "complete",
      "is_profile_complete": true,
      "is_minimal_profile": false,
      "created_at": "2024-01-15 08:00:00"
    }
  },
  "message": "Successfully authenticated"
}
```

**Объяснение профиля:**
- `profile_status`: "complete" - профиль завершен
- `is_profile_complete`: true - все обязательные поля заполнены
- `is_minimal_profile`: false - это не минимальный профиль
- Отсутствует `profile_completion_required` - завершение не требуется

### Шаг 3: Доступ к функциям

Существующий пользователь имеет **полный доступ** ко всем функциям:

**Просмотр вакансий:**
```bash
curl -X GET "http://localhost:8000/worker/vacancy/list?page=1&per_page=10" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

**Добавление в избранное:**
```bash
curl -X POST http://localhost:8000/worker/profile/add-favorite \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." \
  -d "vacancy_id=123"
```

---

## Сценарий 2: Новый пользователь (Мобильное приложение)

### Характеристики нового пользователя:
- Впервые использует систему
- Автоматически создается минимальная запись
- Статус профиля: `incomplete`
- Ограниченный доступ до завершения профиля

### Шаг 1: Запрос кода валидации (автоматическая регистрация)

**HTTP запрос:**
```bash
curl -X POST http://localhost:8000/worker/auth/send-code \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "phone=+998901234999"
```

**Ответ сервера (200 OK):**
```json
{
  "success": true,
  "data": {
    "message": "Validation code sent to your phone",
    "phone": "+998901234999",
    "expires_in": 300,
    "next_request_in": 120,
    "is_new_registration": true,
    "profile_status": "incomplete",
    "registration_info": {
      "message": "Welcome! Please complete your profile after login",
      "telegram_bot_url": "https://t.me/your_bot_name",
      "requires_profile_completion": true
    }
  },
  "message": "Code sent successfully"
}
```

**Объяснение отличий:**
- `is_new_registration`: true - это новый пользователь
- `profile_status`: "incomplete" - профиль не завершен
- `registration_info`: дополнительная информация для новых пользователей
  - `message`: приветственное сообщение
  - `telegram_bot_url`: ссылка на Telegram бота для полной регистрации
  - `requires_profile_completion`: требуется завершение профиля

### Шаг 2: Вход в систему с кодом

**HTTP запрос:**
```bash
curl -X POST http://localhost:8000/worker/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "phone=+998901234999" \
  -d "code=5678"
```

**Ответ сервера (200 OK):**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_at": "2024-01-22 10:30:00",
    "worker": {
      "id": 16,
      "name": null,
      "phone": "+998901234999",
      "age": null,
      "experience_years": null,
      "about": null,
      "language": null,
      "location": {
        "lat": null,
        "lng": null
      },
      "audio_file_url": null,
      "professions": [],
      "profile_status": "incomplete",
      "is_profile_complete": false,
      "is_minimal_profile": true,
      "created_at": "2024-01-22 09:15:00"
    },
    "profile_completion_required": true,
    "profile_completion_message": "Please complete your profile to access all features"
  },
  "message": "Successfully authenticated"
}
```

**Объяснение минимального профиля:**
- Большинство полей `null` (только телефон заполнен)
- `profile_status`: "incomplete"
- `is_profile_complete`: false
- `is_minimal_profile`: true
- `profile_completion_required`: true - требуется завершение
- `profile_completion_message`: сообщение о необходимости завершения

### Шаг 3: Ограниченный доступ

Новый пользователь имеет **ограниченный доступ**:

**✅ Разрешенные действия:**

**Просмотр профиля:**
```bash
curl -X GET http://localhost:8000/worker/profile/index \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

**❌ Заблокированные действия:**

**Попытка просмотра вакансий:**
```bash
curl -X GET http://localhost:8000/worker/vacancy/list \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

**Ответ (403 Forbidden):**
```json
{
  "success": false,
  "data": {
    "profile_completion_required": true,
    "message": "Please complete your profile to access this feature",
    "profile_status": "incomplete",
    "completion_steps": {
      "name": true,
      "age": true,
      "about": true,
      "professions": true
    }
  },
  "message": "Profile completion required"
}
```

**Объяснение блокировки:**
- `completion_steps`: показывает, какие поля нужно заполнить
- `true` означает, что поле не заполнено и требует внимания

### Шаг 4: Завершение профиля

**Обновление основной информации:**
```bash
curl -X PUT http://localhost:8000/worker/profile/update \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "name=Алексей Сидоров" \
  -d "age=25" \
  -d "about=Молодой специалист, готов к обучению" \
  -d "profession_ids[]=1" \
  -d "profession_ids[]=2"
```

**Ответ после завершения профиля:**
```json
{
  "success": true,
  "data": {
    "id": 16,
    "name": "Алексей Сидоров",
    "phone": "+998901234999",
    "age": 25,
    "about": "Молодой специалист, готов к обучению",
    "professions": [
      {
        "id": 1,
        "name_ru": "Строитель",
        "name_uz": "Quruvchi",
        "name_en": "Builder"
      }
    ],
    "profile_status": "complete",
    "is_profile_complete": true,
    "is_minimal_profile": false
  },
  "message": "Profile updated successfully"
}
```

**Изменения после завершения:**
- `profile_status`: изменился на "complete"
- `is_profile_complete`: стал true
- `is_minimal_profile`: стал false
- Теперь доступны все функции системы

---

## Ключевые различия между типами пользователей

| Аспект | Существующий пользователь | Новый пользователь |
|--------|---------------------------|-------------------|
| **Регистрация** | Через Telegram бота | Автоматическая при первом входе |
| **Профиль при входе** | Полностью заполнен | Только номер телефона |
| **Статус профиля** | `complete` | `incomplete` |
| **Доступ к функциям** | Полный | Ограниченный |
| **Поле в ответе send-code** | `is_new_registration: false` | `is_new_registration: true` |
| **Дополнительная информация** | Отсутствует | `registration_info` объект |
| **Требования** | Нет | Завершение профиля |

## Автоматическое определение статуса

Система автоматически определяет и обновляет статус профиля:

1. **При создании** - статус `incomplete`
2. **При обновлении** - проверяет заполненность всех обязательных полей
3. **При заполнении** - автоматически меняет статус на `complete`

**Обязательные поля для завершения:**
- Имя (`name`)
- Возраст (`age`) 
- Описание (`about`)
- Хотя бы одна профессия

Это обеспечивает плавный переход от минимального профиля к полному без дополнительных действий от пользователя.
